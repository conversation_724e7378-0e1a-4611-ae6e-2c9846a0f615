# Integrasi Server PHPNuxBill dengan Aplikasi Mobile

Dokumen ini menjelaskan cara mengintegrasikan server PHPNuxBill Anda dengan aplikasi mobile.

## Persiapan Server

### 1. Pastikan Server Dapat Diakses dari Internet

Aplikasi mobile memerlukan akses ke server PHPNuxBill Anda. Pastikan server Anda:

- Memiliki alamat IP publik atau domain yang dapat diakses
- Port 80 (HTTP) atau 443 (HTTPS) terbuka dan dapat diakses
- Sebaiknya menggunakan HTTPS untuk keamanan

### 2. Optimasi Tampilan untuk Mobile

Tambahkan kode berikut ke file template Anda untuk memastikan tampilan responsif:

```html
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
```

### 3. Aktifkan CORS (Cross-Origin Resource Sharing)

Tambahkan header berikut ke file `.htaccess` di root direktori PHPNuxBill Anda:

```
<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type, Authorization"
</IfModule>
```

## Pengaturan API (Opsional)

Jika Anda ingin mengembangkan aplikasi lebih lanjut dengan API native, Anda perlu membuat endpoint API:

### 1. Buat File API Endpoint

Buat file `api.php` di direktori `system` dengan konten berikut:

```php
<?php
/**
 * PHPNuxBill API Endpoint for Mobile App
 */

// Include necessary files
require_once '../init.php';

// Set content type to JSON
header('Content-Type: application/json');

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Get request path
$path = isset($_GET['path']) ? $_GET['path'] : '';

// Handle different API endpoints
switch ($path) {
    case 'auth/login':
        // Handle login
        if ($method === 'POST') {
            $data = json_decode(file_get_contents('php://input'), true);
            $username = $data['username'] ?? '';
            $password = $data['password'] ?? '';
            
            // Authenticate user
            // Return token or error
        }
        break;
        
    case 'user/profile':
        // Get user profile
        if ($method === 'GET') {
            // Verify token
            // Return user profile data
        }
        break;
        
    // Add more endpoints as needed
        
    default:
        // Handle unknown endpoint
        http_response_code(404);
        echo json_encode(['error' => 'Endpoint not found']);
        break;
}
```

### 2. Buat Sistem Autentikasi Token

Untuk API yang aman, implementasikan sistem token:

```php
function generateToken($userId) {
    $key = 'your_secret_key'; // Ganti dengan kunci rahasia yang kuat
    $issuedAt = time();
    $expirationTime = $issuedAt + 3600; // Token berlaku 1 jam
    
    $payload = [
        'user_id' => $userId,
        'iat' => $issuedAt,
        'exp' => $expirationTime
    ];
    
    // Enkripsi payload sederhana (dalam produksi, gunakan JWT yang tepat)
    return base64_encode(json_encode($payload)) . '.' . hash_hmac('sha256', base64_encode(json_encode($payload)), $key);
}

function validateToken($token) {
    $key = 'your_secret_key';
    $parts = explode('.', $token);
    
    if (count($parts) !== 2) {
        return false;
    }
    
    $payload = json_decode(base64_decode($parts[0]), true);
    $signature = $parts[1];
    
    // Verifikasi signature
    $expectedSignature = hash_hmac('sha256', $parts[0], $key);
    
    if ($signature !== $expectedSignature) {
        return false;
    }
    
    // Periksa expiration
    if (isset($payload['exp']) && $payload['exp'] < time()) {
        return false;
    }
    
    return $payload;
}
```

## Pengujian Integrasi

Setelah server Anda siap, uji integrasi dengan aplikasi mobile:

1. Pastikan URL server diatur dengan benar di `MainActivity.java`
2. Jalankan aplikasi di perangkat atau emulator
3. Verifikasi bahwa login dan fitur lain berfungsi dengan baik

## Pemecahan Masalah

### Masalah Koneksi
- Periksa apakah server dapat diakses dari internet
- Verifikasi bahwa URL server diketik dengan benar di aplikasi
- Periksa log server untuk kesalahan

### Masalah Login
- Pastikan cookie diaktifkan di WebView
- Periksa apakah form login berfungsi di browser desktop
- Verifikasi bahwa kredensial login valid

### Masalah Tampilan
- Periksa apakah meta viewport ditambahkan ke template
- Sesuaikan CSS untuk tampilan mobile jika perlu
- Gunakan Chrome Remote Debugging untuk WebView untuk mendiagnosis masalah

## Pengembangan Lanjutan

Untuk pengembangan lebih lanjut, pertimbangkan:

1. Implementasi notifikasi push menggunakan Firebase Cloud Messaging
2. Pengembangan API lengkap untuk operasi native
3. Penyimpanan data offline untuk penggunaan tanpa koneksi
