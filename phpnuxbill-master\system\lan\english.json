{"Settings": "Settings", "Search_Users": "Search Users", "Cancel": "Cancel", "SuperAdmin": "SuperAdmin", "Change_Password": "Change Password", "My_Account": "My Account", "Logout": "Logout", "Dashboard": "Dashboard", "Customer": "Customer", "Services": "Services", "Active_Customers": "Active Customers", "Refill_Customer": "Refill Customer", "Vouchers": "Vouchers", "Recharge_Customer": "Recharge Customer", "Refill_Balance": "Refill Balance", "Internet_Plan": "Internet Plan", "Customer_Balance": "Customer Balance", "Maps": "Maps", "Routers": "Routers", "Reports": "Reports", "Daily_Reports": "Daily Reports", "Activation_History": "Activation History", "Send_Message": "Send Message", "Single_Customer": "Single Customer", "Bulk_Customers": "Bulk Customers", "Network": "Network", "Radius": "<PERSON><PERSON>", "Radius_NAS": "Radius NAS", "Static_Pages": "Static Pages", "Order_Voucher": "Order Voucher", "Theme_Voucher": "Theme Voucher", "Announcement": "Announcement", "Customer_Announcement": "Customer Announcement", "Registration_Info": "Registration Info", "Payment_Info": "Payment Info", "Privacy_Policy": "Privacy Policy", "Terms_and_Conditions": "Terms and Conditions", "General_Settings": "General Settings", "Localisation": "Localisation", "Custom_Fields": "Custom Fields", "Miscellaneous": "Miscellaneous", "Maintenance_Mode": "Maintenance Mode", "Widgets": "Widgets", "User_Notification": "User Notification", "Devices": "Devices", "Administrator_Users": "Administrator Users", "Backup_Restore": "Backup/Restore", "Payment_Gateway": "Payment Gateway", "Plugin_Manager": "Plugin Manager", "Logs": "Logs", "Documentation": "Documentation", "General": "General", "Application_Name___Company_Name": "Application Name / Company Name", "This_Name_will_be_shown_on_the_Title": "This Name will be shown on the Title", "Company_Logo": "Company Logo", "For_PDF_Reports___Best_size_1078_x_200___uploaded_image_will_be_autosize": "For PDF Reports | Best size 1078 x 200 | uploaded image will be autosize", "Company_Footer": "Company Footer", "Will_show_below_user_pages": "Will show below user pages", "Address": "Address", "You_can_use_html_tag": "You can use html tag", "Phone_Number": "Phone Number", "Invoice_Footer": "Invoice Footer", "Print_Max_Char": "Print Max Char", "For_invoice_print_using_Thermal_Printer": "For invoice print using Thermal Printer", "Theme": "Theme", "Default": "<PERSON><PERSON><PERSON>", "Theme_Info": "Theme Info", "Recharge_Using": "Recharge Using", "Cash": "Cash", "Bank_Transfer": "Bank Transfer", "This_used_for_admin_to_select_payment_in_recharge__using_comma_for_every_new_options": "This used for admin to select payment in recharge, using comma for every new options", "Income_reset_date": "Income reset date", "Income_will_reset_every_this_day": "Income will reset every this day", "Dashboard_Structure": "Dashboard Structure", "Read_documentation": "Read documentation", "Pretty_URL": "Pretty URL", "No": "No", "Yes": "Yes", "rename__htaccess_firewall_to__htaccess": "rename .htaccess_firewall to .htaccess", "Save_Changes": "Save Changes", "Customer_Login_Page_Settings": "Customer Login <PERSON> Settings", "Choose_Template": "<PERSON><PERSON>", "Custom": "Custom", "Select_your_login_template_type": "Select your login template type", "Select_Login_Page": "Select Login Page", "Select_your_preferred_login_template": "Select your preferred login template", "Page_Heading___Company_Name": "Page Heading / Company Name", "This_Name_will_be_shown_on_the_login_wallpaper": "This Name will be shown on the login wallpaper", "Page_Description": "Page Description", "This_will_also_display_on_wallpaper__You_can_use_html_tag": "This will also display on wallpaper, You can use html tag", "Favicon": "Favicon", "Best_size_30_x_30___uploaded_image_will_be_autosize": "Best size 30 x 30 | uploaded image will be autosize", "Login_Page_Logo": "Login <PERSON>", "Best_size_300_x_60___uploaded_image_will_be_autosize": "Best size 300 x 60 | uploaded image will be autosize", "Login_Page_Wallpaper": "Login Page Wallpaper", "Best_size_1920_x_1080___uploaded_image_will_be_autosize": "Best size 1920 x 1080 | uploaded image will be autosize", "Registration": "Registration", "Allow_Registration": "Allow Registration", "Voucher_Only": "Voucher Only", "No_Registration": "No Registration", "Customer_just_Login_with_Phone_number_and_Voucher_Code__Voucher_will_be_password": "Customer just Login with Phone number and Voucher Code, Voucher will be password", "Registration_Username": "Registration Username", "Photo_Required": "Photo Required", "Customer_Registration_need_to_upload_their_photo": "Customer Registration need to upload their photo", "SMS_OTP_Registration": "SMS OTP Registration", "Customer_Registration_need_to_validate_using_OTP": "Customer Registration need to validate using OTP", "OTP_Method": "OTP Method", "By_SMS": "By SMS", "by_WhatsApp": "by WhatsApp", "By_WhatsApp_and_SMS": "By WhatsApp and SMS", "The_method_which_OTP_will_be_sent_to_user": "The method which OTP will be sent to user", "For_Registration_and_Update_Phone_Number": "For Registration and Update Phone Number", "Notify_Admin": "Notify Admin", "Notify_Admin_upon_self_registration": "Notify Admin upon self registration", "Mandatory_Fields": "Mandatory Fields", "Email": "Email", "Full_Name": "Full Name", "Security": "Security", "Enable_Session_Timeout": "Enable Session Timeout", "Logout_Admin_if_not_Available_Online_a_period_of_time": "Logout Admin if not Available/Online a period of time", "Timeout_Duration": "Timeout Duration", "Enter_the_session_timeout_duration__minutes_": "Enter the session timeout duration (minutes)", "Idle_Timeout__Logout_Admin_if_Idle_for_xx_minutes": "Idle Timeout, <PERSON><PERSON><PERSON>min if Idle for xx minutes", "Single_Admin_Session": "Single Admin Session", "Admin_can_only_have_single_session_login__it_will_logout_another_session": "Admin can only have single session login, it will logout another session", "Enable_CSRF_Validation": "Enable CSRF Validation", "Cross_site_request_forgery": "Cross-site request forgery", "Disable_Voucher": "Disable Voucher", "Voucher_activation_menu_will_be_hidden": "Voucher activation menu will be hidden", "Voucher_Format": "Voucher Format", "Redirect_URL_after_Activation": "Redirect URL after Activation", "After_Customer_activate_voucher_or_login__customer_will_be_redirected_to_this_url": "After Customer activate voucher or login, customer will be redirected to this url", "Enable_Radius": "Enable <PERSON>", "Radius_Instructions": "Radius Instructions", "Extend_Postpaid_Expiration": "Extend Postpaid Expiration", "Allow_Extend": "Allow Extend", "Customer_can_request_to_extend_expirations": "Customer can request to extend expirations", "Extend_Days": "Extend Days", "Confirmation_Message": "Confirmation Message", "i_agree_to_extends_and_will_paid_full_after_this": "i agree to extends and will paid full after this", "Customer_Balance_System": "Customer Balance System", "Enable_System": "Enable System", "Customer_can_deposit_money_to_buy_voucher": "Customer can deposit money to buy voucher", "Allow_Transfer": "Allow Transfer", "Allow_balance_transfer_between_customers": "Allow balance transfer between customers", "Minimum_Balance_Transfer": "Minimum Balance Transfer", "Allow_Balance_Custom_Amount": "Allow Balance Custom Amount", "Allow_Customer_buy_balance_with_any_amount": "Allow Customer buy balance with any amount", "Telegram_Notification": "Telegram Notification", "Telegram_Bot_Token": "Telegram Bot Token", "Telegram_User_Channel_Group_ID": "Telegram User/Channel/Group ID", "You_will_get_Payment_and_Error_notification": "You will get Payment and Error notification", "SMS_Notification": "SMS Notification", "Test_SMS": "Test SMS", "SMS_Server_URL": "SMS Server URL", "Must_include": "Must include", "it_will_be_replaced_": "it will be replaced.", "Or_use_Mikrotik_SMS": "Or use Mikrotik SMS", "Select_Router": "Select Router", "Mikrotik_SMS_Command": "Mikrotik SMS Command", "You_can_use": "You can use", "in_here_too_": "in here too.", "Free_Server": "Free Server", "Whatsapp_Notification": "Whatsapp Notification", "WhatsApp_Server_URL": "WhatsApp Server URL", "Email_Notification": "Email Notification", "Empty_this_to_use_internal_mail___PHP": "Empty this to use internal mail() PHP", "SMTP_Username": "SMTP Username", "SMTP_Password": "SMTP Password", "SMTP_Security": "SMTP Security", "From": "From", "Mail_Reply_To": "Mail Reply To", "Customer_will_reply_email_to_this_address__empty_if_you_want_to_use_From_Address": "Customer will reply email to this address, empty if you want to use From Address", "Expired_Notification": "Expired Notification", "None": "None", "By_WhatsApp": "By WhatsApp", "By_Email": "By Email", "User_will_get_notification_when_package_expired": "User will get notification when package expired", "Payment_Notification": "Payment Notification", "User_will_get_invoice_notification_when_buy_package_or_package_refilled": "User will get invoice notification when buy package or package refilled", "Reminder_Notification": "Reminder Notification", "Reminder_Notify_Intervals": "Reminder Notify Intervals", "1_Day": "1 Day", "3_Days": "3 Days", "7_Days": "7 Days", "Tawk_to_Chat_Widget": "Tawk.to Cha<PERSON> Widget", "From_Direct_Chat_Link_": "From Direct Chat Link.", "Access_Token": "Access Token", "Empty_this_to_randomly_created_API_key": "Empty this to randomly created API key", "This_Token_will_act_as_SuperAdmin_Admin": "This Token will act as SuperAdmin/Admin", "Proxy": "Proxy", "Proxy_Server": "Proxy Server", "Proxy_Server_Login": "Proxy Server Login", "Tax_System": "Tax System", "Enable_Tax_System": "Enable Tax System", "Tax_will_be_calculated_in_Internet_Plan_Price": "Tax will be calculated in Internet Plan Price", "Tax_Rate": "Tax Rate", "Custome": "Custome", "Tax_Rates_by_percentage": "Tax Rates by percentage", "Custome_Tax_Rate": "Custome Tax Rate", "Enter_Custome_Tax_Rate": "Enter Custome Tax Rate", "Enter_the_custom_tax_rate__e_g___3_75_for_3_75__": "Enter the custom tax rate (e.g., 3.75 for 3.75%)", "Authentication": "Authentication", "Github_Username": "<PERSON><PERSON><PERSON>", "Github_Token": "<PERSON><PERSON><PERSON>", "Create_GitHub_personal_access_token": "Create GitHub personal access token", "only_need_repo_scope": "only need repo scope", "This_will_allow_you_to_download_plugin_from_private_paid_repository": "This will allow you to download plugin from private/paid repository", "Settings_For_Mikrotik": "Settings For Mikrotik", "Settings_For_Cron_Expired": "Settings For Cron Expired", "Expired_Cronjob_Every_5_Minutes__Recommended_": "Expired <PERSON><PERSON>job Every 5 Minutes [Recommended]", "Expired_Cronjob_Every_1_Hour": "Expired <PERSON><PERSON><PERSON>b Every 1 Hour", "Settings_For_Cron_Reminder": "Settings For <PERSON><PERSON>er", "Reminder_Cronjob_Every_7_AM": "Reminder <PERSON><PERSON><PERSON><PERSON> Every 7 AM", "will_be_replaced_with_Customer_Name": "will be replaced with Customer Name", "will_be_replaced_with_Customer_username": "will be replaced with Customer username", "will_be_replaced_with_Package_name": "will be replaced with Package name", "will_be_replaced_with_Package_price": "will be replaced with Package price", "will_be_replaced_with_Expiration_date": "will be replaced with Expiration date", "additional_bills_for_customers": "additional bills for customers", "Minute": "Minute", "Hour": "Hour", "Day": "Day", "Before": "Before", "After": "After", "Save": "Save", "Your_Account_Information": "Your Account Information", "Usernames": "Usernames", "Password": "Password", "Service_Type": "Service Type", "Yours_Balances": "Yours Balances", "Disable_auto_renewal_": "Disable auto renewal?", "Auto_Renewal_On": "Auto Renewal On", "Additional_Billing": "Additional Billing", "paid_off": "paid off", "Total": "Total", "Voucher_Activation": "Voucher Activation", "Enter_voucher_code_here": "Enter voucher code here", "Recharge": "Recharge", "Order_Package": "Order Package", "Package_Name": "Package Name", "Expired": "Expired", "Bandwidth": "Bandwidth", "Created_On": "Created On", "Expires_On": "Expires On", "Type": "Type", "Sync_account_if_you_failed_login_to_internet": "Sync account if you failed login to internet", "Sync": "Sync", "Deactivate": "Deactivate", "Transfer_Balance": "Transfer Balance", "Friend_Usernames": "<PERSON>", "Balance_Amount": "Balance Amount", "_Are_You_Sure_": " Are You Sure?", "Send_yours_balance___": "Send yours balance ? ", "Recharge_a_friend": "Recharge a friend", "Friend_username": "Friend username", "Inbox": "Inbox", "Buy_Balance": "Buy Balance", "Buy_Package": "Buy Package", "Payment_History": "Payment History", "_": "-", "Order_Internet_Package": "Order Internet Package", "Price": "Price", "Validity": "Validity", "Buy_this__your_active_package_will_be_overwritten": "Buy this? your active package will be overwritten", "Buy": "Buy", "Buy_this__your_active_package_will_be_overwrite": "Buy this? your active package will be overwrite", "Buy_this_for_friend_account_": "Buy this for friend account?", "Buy_for_friend": "Buy for friend", "Validity_Periode": "Validity Periode", "Are_You_Sure_": "Are You Sure?", "If_your_friend_have_Additional_Cost__you_will_pay_for_that_too": "If your friend have Additional Cost, you will pay for that too", "Username_not_found": "<PERSON><PERSON><PERSON> not found", "This_will_export_to_CSV": "This will export to CSV", "Manage_Contact": "Manage Contact", "Order_": "Order ", "Username": "Username", "First_Name": "First Name", "Last_Name": "Last Name", "Created_Date": "Created Date", "Balance": "Balance", "Status": "Status", "Ascending": "Ascending", "Descending": "Descending", "Active": "Active", "Banned": "Banned", "Disabled": "Disabled", "Inactive": "Inactive", "Limited": "Limited", "Suspended": "Suspended", "Search": "Search", "Add": "Add", "Account_Type": "Account Type", "Contact": "Contact", "Package": "Package", "Manage": "Manage", "View": "View", "Edit": "Edit", "Delete_Selected": "Delete Selected", "Prev": "Prev", "Next": "Next", "All": "All", "SMS": "SMS", "WhatsApp": "WhatsApp", "Enter_your_message_here___": "Enter your message here...", "Close": "Close", "Please_select_at_least_one_customer_to_send_a_message_": "Please select at least one customer to send a message.", "Please_enter_a_message_to_send_": "Please enter a message to send.", "Sending___": "Sending...", "Message_sent_successfully_": "Message sent successfully.", "Error_sending_message__": "Error sending message: ", "Failed_to_send_the_message__Please_try_again_": "Failed to send the message. Please try again.", "City": "City", "District": "District", "State": "State", "Zip": "Zip", "Others": "Others", "Personal": "Personal", "Auto_Renewal": "Auto Renewal", "Last_Login": "Last Login", "Delete": "Delete", "Order_History": "Order History", "Invoice": "Invoice", "Plan_Name": "Plan Name", "Plan_Price": "Plan Price", "Method": "Method", "Back": "Back", "This_will_sync_Customer_to_Mikrotik": "This will sync Custom<PERSON> to Mi<PERSON><PERSON>ik", "Login_as_Customer": "<PERSON><PERSON> as Customer", "Edit_Contact": "Edit Contact", "Photo": "Photo", "Face_Detection": "Face Detection", "Keep_Blank_to_do_not_change_Password": "Keep Blank to do not change Password", "Home_Address": "Home Address", "Other": "Other", "Business": "Business", "Coordinates": "Coordinates", "Customer_cannot_login_again": "Customer cannot login again", "Customer_can_login_but_cannot_buy_internet_package__Admin_cannot_recharge_customer": "Customer can login but cannot buy internet package, Admin cannot recharge customer", "Don_t_forget_to_deactivate_all_active_package_too": "Don't forget to deactivate all active package too", "Not_Working_with_Freeradius_Mysql": "Not Working with Freeradius Mysql", "User_Cannot_change_this__only_admin__if_it_Empty_it_will_use_Customer_Credentials": "User Cannot change this, only admin. if it Empty it will use Customer Credentials", "Attributes": "Attributes", "Additional_Information": "Additional Information", "City_of_Resident": "City of Resident", "State_of_Resident": "State of Resident", "Zip_Code": "Zip Code", "Continue_the_Customer_Data_change_process_": "Continue the Customer Data change process?", "Make_sure_you_use_API_Port__Default_8728": "Make sure you use API Port, Default 8728", "Make_sure_Username_and_Password_are_correct": "Make sure Userna<PERSON> and Password are correct", "Make_sure_your_hosting_not_blocking_port_to_external": "Make sure your hosting not blocking port to external", "Make_sure_your_Mikrotik_accessible_from_PHPNuxBill": "Make sure your Mikrotik accessible from PHPNuxBill", "If_you_just_update_PHPNuxBill_from_upload_files__try_click_Update": "If you just update PHPNuxBill from upload files, try click Update", "Update": "Update", "Update_PHPNuxBill": "Update PHPNuxBill", "Ask_Github_Community": "Ask Github Community", "Ask_Telegram_Community": "Ask Telegram Community", "Success_to_send_package": "Success to send package", "Payment_not_found": "Payment not found", "Target_has_active_plan__different_with_current_plant_": "Target has active plan, different with current plant.", "Login": "<PERSON><PERSON>", "Log_in_to_Member_Panel": "Log in to Member Panel", "Register": "Register", "Forgot_Password": "Forgot Password", "Payment_Method": "Payment Method", "Package_Price": "Package Price", "Created_on": "Created on", "Expires_on": "Expires on", "Date": "Date", "PAID": "PAID", "CANCELED": "CANCELED", "Voucher": "Voucher", "Transaction_History_List": "Transaction History List", "Transaction": "Transaction", "Paid_Date": "Paid <PERSON>", "To": "To", "Message_Log": "Message Log", "Keep_Logs": "Keep Logs", "Days": "Days", "_Clear_old_logs_": " Clear old logs?", "Clean_up_Logs": "Clean up Logs", "ID": "ID", "Date_Sent": "Date Sent"}