package com.phpnuxbill.mobile;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Bitmap;
import android.net.Uri;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ProgressBar;
import android.view.View;

/**
 * Custom WebViewClient that handles offline mode and error pages
 */
public class OfflineWebViewClient extends WebViewClient {

    private Context context;
    private ProgressBar progressBar;
    private String baseUrl;
    private boolean isOffline = false;

    public OfflineWebViewClient(Context context, ProgressBar progressBar, String baseUrl) {
        this.context = context;
        this.progressBar = progressBar;
        this.baseUrl = baseUrl;
    }

    @Override
    public void onPageStarted(WebView view, String url, Bitmap favicon) {
        super.onPageStarted(view, url, favicon);
        progressBar.setVisibility(View.VISIBLE);
    }

    @Override
    public void onPageFinished(WebView view, String url) {
        super.onPageFinished(view, url);
        progressBar.setVisibility(View.GONE);
        
        // Inject CSS to make the web page more mobile-friendly
        String css = "body { font-size: 16px; } " +
                     ".container { width: 100% !important; padding: 0 10px; } " +
                     "input, select, textarea { font-size: 16px !important; } " +
                     ".btn { padding: 10px 15px !important; } " +
                     "table { width: 100% !important; overflow-x: auto !important; display: block !important; } " +
                     "@media (max-width: 768px) { .hidden-xs { display: none !important; } }";
        
        view.loadUrl("javascript:(function() {" +
                "var style = document.createElement('style');" +
                "style.innerHTML = '" + css + "';" +
                "document.head.appendChild(style);" +
                "})()");
    }

    @Override
    public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
        String url = request.getUrl().toString();
        
        // Check if the URL is from the same domain
        Uri baseUri = Uri.parse(baseUrl);
        Uri requestUri = request.getUrl();
        
        if (baseUri.getHost() != null && baseUri.getHost().equals(requestUri.getHost())) {
            // Same domain, load in WebView
            return false;
        } else {
            // External link, open in browser
            return true;
        }
    }

    @Override
    public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
        super.onReceivedError(view, request, error);
        
        // Only show error for main frame, not for resources
        if (request.isForMainFrame() && !isOffline) {
            isOffline = true;
            showOfflineDialog();
        }
    }

    private void showOfflineDialog() {
        progressBar.setVisibility(View.GONE);
        
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setTitle(R.string.no_internet)
               .setMessage(R.string.check_connection)
               .setPositiveButton(R.string.retry, new DialogInterface.OnClickListener() {
                   @Override
                   public void onClick(DialogInterface dialog, int which) {
                       if (NetworkUtils.isNetworkAvailable(context)) {
                           isOffline = false;
                           progressBar.setVisibility(View.VISIBLE);
                           ((WebView) ((Activity) context).findViewById(R.id.webView)).loadUrl(baseUrl);
                       } else {
                           showOfflineDialog();
                       }
                   }
               })
               .setNegativeButton(R.string.exit, new DialogInterface.OnClickListener() {
                   @Override
                   public void onClick(DialogInterface dialog, int which) {
                       ((Activity) context).finish();
                   }
               })
               .setCancelable(false)
               .show();
    }
}
