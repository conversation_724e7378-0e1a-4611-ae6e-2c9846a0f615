<!DOCTYPE html>
<html lang="en" class="has-aside-left has-aside-mobile-transition has-navbar-fixed-top has-aside-expanded">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{$_title} - {$_c['CompanyName']}</title>

    <script>
        var appUrl = '{$app_url}';
    </script>

    <link rel="shortcut icon" href="{$app_url}/ui/ui/images/logo.png" type="image/x-icon" />
    <link rel="stylesheet" href="{$app_url}/ui/ui/styles/bootstrap.min.css">
    <link rel="stylesheet" href="{$app_url}/ui/ui/fonts/ionicons/css/ionicons.min.css">
    <link rel="stylesheet" href="{$app_url}/ui/ui/fonts/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="{$app_url}/ui/ui/styles/modern-AdminLTE.min.css">
    <link rel="stylesheet" href="{$app_url}/ui/ui/styles/sweetalert2.min.css" />
    <script src="{$app_url}/ui/ui/scripts/sweetalert2.all.min.js"></script>
    <script src="{$app_url}/ui/ui/scripts/modern-dashboard.js?2025.3.13"></script>
    <script src="{$app_url}/ui/ui/scripts/mobile-table-scroll.js?2025.3.13"></script>
    <script>
        // Set global language variable for JavaScript
        window.phpnuxbillLanguage = '{$config.language}';
    </script>
    <link rel="stylesheet" href="{$app_url}/ui/ui/styles/phpnuxbill.customer.css?2025.2.4" />
    <link rel="stylesheet" href="{$app_url}/ui/ui/styles/modern-components.css?2025.3.13" />

    <style>

    </style>

    {if isset($xheader)}
        {$xheader}
    {/if}

</head>

<body class="hold-transition modern-skin-dark sidebar-mini">
    <div class="wrapper">
        <header class="main-header modern-header" style="position:fixed; width: 100%">
            <!-- Desktop Logo -->
            <a href="{Text::url('home')}" class="logo desktop-logo">
                <span class="logo-mini"><b>N</b>uX</span>
                <span class="logo-lg">{$_c['CompanyName']}</span>
            </a>

            <!-- Mobile Header -->
            <div class="mobile-header">
                <a href="{Text::url('home')}" class="mobile-logo">
                    <span class="mobile-logo-text">{$_c['CompanyName']}</span>
                </a>
                <div class="mobile-header-actions">
                    <div class="mobile-balance" {if $_c['enable_balance'] != 'yes'}style="display: none;"{/if}>
                        <span class="mobile-balance-amount">{if $_c['enable_balance'] == 'yes'}{Lang::moneyFormat($_user['balance'])}{/if}</span>
                    </div>
                    <button class="mobile-notifications-btn" onclick="toggleMobileNotifications()">
                        <i class="fa fa-bell"></i>
                        <span class="mobile-notification-badge" api-get-text="{Text::url('autoload_user/inbox_unread')}"></span>
                    </button>
                    <div class="mobile-user-menu dropdown">
                        <img src="{$app_url}/{$UPLOAD_PATH}{$_user['photo']}.thumb.jpg"
                            class="mobile-user-avatar dropdown-toggle"
                            alt="User Image"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="false"
                            data-avatar="true"
                            data-avatar-name="{$_user['fullname']}"
                            data-avatar-type="customer"
                            data-avatar-size="md">

                        <!-- Mobile User Dropdown Menu -->
                        <div class="dropdown-menu mobile-user-dropdown-menu">
                            <div class="mobile-user-info">
                                <img src="{$app_url}/{$UPLOAD_PATH}{$_user['photo']}.thumb.jpg"
                                    class="mobile-user-avatar-large"
                                    alt="User Image"
                                    data-avatar="true"
                                    data-avatar-name="{$_user['fullname']}"
                                    data-avatar-type="customer"
                                    data-avatar-size="xl">
                                <div class="mobile-user-details">
                                    <div class="mobile-user-name">{$_user['fullname']}</div>
                                    <div class="mobile-user-contact">{$_user['phonenumber']}</div>
                                    {if $_c['enable_balance'] == 'yes'}
                                        <div class="mobile-user-balance">Balance: {Lang::moneyFormat($_user['balance'])}</div>
                                    {/if}
                                </div>
                            </div>
                            <div class="mobile-user-actions">
                                <a href="{Text::url('accounts/change-password')}" class="mobile-user-action">
                                    <i class="fa fa-key"></i>
                                    <span>{Lang::T('Change Password')}</span>
                                </a>
                                <a href="{Text::url('accounts/profile')}" class="mobile-user-action">
                                    <i class="fa fa-user"></i>
                                    <span>{Lang::T('My Account')}</span>
                                </a>
                                <a href="{Text::url('logout')}" class="mobile-user-action logout">
                                    <i class="fa fa-sign-out"></i>
                                    <span>{Lang::T('Logout')}</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <nav class="navbar navbar-static-top">
                <!-- Desktop Sidebar Toggle (Hidden on Mobile) -->
                <a href="#" class="sidebar-toggle desktop-sidebar-toggle" data-toggle="push-menu" role="button">
                    <span class="sr-only">Toggle navigation</span>
                </a>
                <div class="navbar-custom-menu">
                    <ul class="nav navbar-nav desktop-nav">
                        <!-- Dark Mode Toggle -->
                        <li>
                            <a class="toggle-container" href="#">
                                <i class="toggle-icon" id="toggleIcon">🌜</i>
                            </a>
                        </li>

                        <!-- Language Selector -->
                        <li class="dropdown tasks-menu">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown" aria-expanded="true">
                                <i class="fa fa-flag-o"></i>
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <ul class="menu" api-get-text="{Text::url('autoload_user/language&select=',$user_language)}"></ul>
                                </li>
                            </ul>
                        </li>

                        <!-- Notifications -->
                        <li class="dropdown notifications-menu">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                <i class="fa fa-envelope-o"></i>
                                <span class="label label-warning"
                                    api-get-text="{Text::url('autoload_user/inbox_unread')}"></span>
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <ul class="menu" api-get-text="{Text::url('autoload_user/inbox')}"></ul>
                                </li>
                                <li class="footer"><a href="{Text::url('mail')}">{Lang::T('Inbox')}</a></li>
                            </ul>
                        </li>

                        <!-- Desktop User Menu -->
                        <li class="dropdown user user-menu">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                {if $_c['enable_balance'] == 'yes'}
                                    <span class="user-balance">{Lang::moneyFormat($_user['balance'])}</span>
                                {else}
                                    <span>{$_user['fullname']}</span>
                                {/if}
                                <img src="{$app_url}/{$UPLOAD_PATH}{$_user['photo']}.thumb.jpg"
                                    class="user-image"
                                    alt="User Image"
                                    data-avatar="true"
                                    data-avatar-name="{$_user['fullname']}"
                                    data-avatar-type="customer"
                                    data-avatar-size="sm">
                            </a>
                            <ul class="dropdown-menu">
                                <li class="user-header">
                                    <img src="{$app_url}/{$UPLOAD_PATH}{$_user['photo']}.thumb.jpg"
                                        class="img-circle"
                                        alt="User Image"
                                        data-avatar="true"
                                        data-avatar-name="{$_user['fullname']}"
                                        data-avatar-type="customer"
                                        data-avatar-size="lg">
                                    <p>
                                        {$_user['fullname']}
                                        <small>{$_user['phonenumber']}<br>
                                            {$_user['email']}</small>
                                    </p>
                                </li>
                                <li class="user-body">
                                    <div class="row">
                                        <div class="col-xs-7 text-center text-sm">
                                            <a href="{Text::url('accounts/change-password')}"><i class="ion ion-settings"></i>
                                                {Lang::T('Change Password')}</a>
                                        </div>
                                        <div class="col-xs-5 text-center text-sm">
                                            <a href="{Text::url('accounts/profile')}"><i class="ion ion-person"></i>
                                                {Lang::T('My Account')}</a>
                                        </div>
                                    </div>
                                </li>
                                <li class="user-footer">
                                    <div class="pull-right">
                                        <a href="{Text::url('logout')}" class="btn btn-default btn-flat"><i
                                                class="ion ion-power"></i> {Lang::T('Logout')}</a>
                                    </div>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </nav>



            <!-- Mobile Notifications Dropdown -->
            <div class="mobile-notifications-dropdown" id="mobileNotificationsDropdown">
                <div class="mobile-notifications-header">
                    <h3>Notifications</h3>
                    <button onclick="toggleMobileNotifications()" class="mobile-notifications-close">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <div class="mobile-notifications-content" api-get-text="{Text::url('autoload_user/inbox')}">
                    <!-- Notifications will be loaded here -->
                </div>
                <div class="mobile-notifications-footer">
                    <a href="{Text::url('mail')}" class="mobile-notifications-view-all">View All Messages</a>
                </div>
            </div>
        </header>

        <aside class="main-sidebar desktop-sidebar" style="position:fixed;">
            <section class="sidebar">
                <ul class="sidebar-menu" data-widget="tree">
                    <li {if $_system_menu eq 'home'}class="active" {/if}>
                        <a href="{Text::url('home')}">
                            <i class="ion ion-monitor"></i>
                            <span>{Lang::T('Dashboard')}</span>
                        </a>
                    </li>
                    {$_MENU_AFTER_DASHBOARD}
                    <li {if $_system_menu eq 'inbox'}class="active" {/if}>
                        <a href="{Text::url('mail')}">
                            <i class="fa fa-envelope"></i>
                            <span>{Lang::T('Inbox')}</span>
                        </a>
                    </li>
                    {$_MENU_AFTER_INBOX}
                    {if $_c['disable_voucher'] != 'yes'}
                        <li {if $_system_menu eq 'voucher'}class="active" {/if}>
                            <a href="{Text::url('voucher/activation')}">
                                <i class="fa fa-ticket"></i>
                                <span>Voucher</span>
                            </a>
                        </li>
                    {/if}
                    {if $_c['payment_gateway'] != 'none' or $_c['payment_gateway'] == '' }
                        {if $_c['enable_balance'] == 'yes'}
                            <li {if $_system_menu eq 'balance'}class="active" {/if}>
                                <a href="{Text::url('order/balance')}">
                                    <i class="ion ion-ios-cart"></i>
                                    <span>{Lang::T('Buy Balance')}</span>
                                </a>
                            </li>
                        {/if}
                        <li {if $_system_menu eq 'package'}class="active" {/if}>
                            <a href="{Text::url('order/package')}">
                                <i class="ion ion-ios-cart"></i>
                                <span>{Lang::T('Buy Package')}</span>
                            </a>
                        </li>
                        <li {if $_system_menu eq 'history'}class="active" {/if}>
                            <a href="{Text::url('order/history')}">
                                <i class="fa fa-file-text"></i>
                                <span>{Lang::T('Payment History')}</span>
                            </a>
                        </li>
                    {/if}
                    {$_MENU_AFTER_ORDER}
                    <li {if $_system_menu eq 'list-activated'}class="active" {/if}>
                        <a href="{Text::url('voucher/list-activated')}">
                            <i class="fa fa-list-alt"></i>
                            <span>{Lang::T('Activation History')}</span>
                        </a>
                    </li>
                    {$_MENU_AFTER_HISTORY}
                </ul>
            </section>
        </aside>

        <div class="content-wrapper">
            <section class="content-header">
                <h1>
                    {$_title}
                </h1>
            </section>
            <section class="content">


                {if isset($notify)}
                    <script>
                        // Enhanced SweetAlert toast notification
                        Swal.fire({
                            icon: '{if $notify_t == "s"}success{elseif $notify_t == "w"}warning{elseif $notify_t == "i"}info{else}error{/if}',
                            title: '{if $notify_t == "s"}Success!{elseif $notify_t == "w"}Warning!{elseif $notify_t == "i"}Information{else}Error!{/if}',
                            html: '{$notify}',
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 8000,
                            timerProgressBar: true,
                            background: '{if $notify_t == "s"}#f0fdf4{elseif $notify_t == "w"}#fffbeb{elseif $notify_t == "i"}#eff6ff{else}#fef2f2{/if}',
                            color: '{if $notify_t == "s"}#166534{elseif $notify_t == "w"}#92400e{elseif $notify_t == "i"}#1e40af{else}#991b1b{/if}',
                            iconColor: '{if $notify_t == "s"}#10b981{elseif $notify_t == "w"}#f59e0b{elseif $notify_t == "i"}#3b82f6{else}#ef4444{/if}',
                            customClass: {
                                popup: 'swal-enhanced-toast',
                                title: 'swal-enhanced-title',
                                htmlContainer: 'swal-enhanced-content'
                            },
                            didOpen: (toast) => {
                                toast.addEventListener('mouseenter', Swal.stopTimer);
                                toast.addEventListener('mouseleave', Swal.resumeTimer);

                                // Add enhanced styling
                                const style = document.createElement('style');
                                style.textContent = `
                                    .swal-enhanced-toast {
                                        border-radius: 12px !important;
                                        border-left: 4px solid {if $notify_t == "s"}#10b981{elseif $notify_t == "w"}#f59e0b{elseif $notify_t == "i"}#3b82f6{else}#ef4444{/if} !important;
                                        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
                                        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                                    }
                                    .swal-enhanced-title {
                                        font-weight: 600 !important;
                                        font-size: 0.95rem !important;
                                    }
                                    .swal-enhanced-content {
                                        font-size: 0.9rem !important;
                                        line-height: 1.5 !important;
                                    }
                                `;
                                document.head.appendChild(style);
                            }
                        });
                    </script>
{/if}
