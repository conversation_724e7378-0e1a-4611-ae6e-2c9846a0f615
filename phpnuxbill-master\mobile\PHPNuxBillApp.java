package com.phpnuxbill.mobile;

import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;
import android.webkit.WebView;

public class PHPNuxBillApp extends Application {

    private static final String PREF_NAME = "PHPNuxBillPrefs";
    private static final String KEY_FIRST_RUN = "firstRun";
    
    @Override
    public void onCreate() {
        super.onCreate();
        
        // Enable WebView debugging in development
        if (BuildConfig.DEBUG) {
            WebView.setWebContentsDebuggingEnabled(true);
        }
        
        // Check if this is the first run
        SharedPreferences prefs = getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        boolean isFirstRun = prefs.getBoolean(KEY_FIRST_RUN, true);
        
        if (isFirstRun) {
            // Perform first-run setup
            setupFirstRun();
            
            // Mark first run as completed
            SharedPreferences.Editor editor = prefs.edit();
            editor.putBoolean(KEY_FIRST_RUN, false);
            editor.apply();
        }
    }
    
    private void setupFirstRun() {
        // Clear any existing WebView data
        clearWebViewData();
    }
    
    private void clearWebViewData() {
        WebView webView = new WebView(this);
        webView.clearCache(true);
        webView.clearHistory();
        webView.clearFormData();
        webView.destroy();
    }
}
