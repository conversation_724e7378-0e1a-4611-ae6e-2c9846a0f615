{"_type": "export", "__export_format": 4, "__export_date": "2025-03-11T07:38:04.536Z", "__export_source": "insomnia.desktop.app:v2022.7.5", "resources": [{"_id": "req_b1a123a4680e47858199ae1bef19e8d5", "parentId": "wrk_08fc208cb0084a0f97abc58ad2e4ba1f", "modified": 1741676224920, "created": 1741675671281, "url": "{{ _.api_url }}", "name": "template api", "description": "", "method": "GET", "body": {}, "parameters": [{"id": "pair_a5d79aa80cfe404cbb9cdcdcb18d23ef", "name": "r", "value": "", "description": ""}, {"id": "pair_d66de068199f43128d5690e27cf07fa0", "name": "token", "value": "{% response 'body', 'req_60bdc4349a2d47129e87c1ab3d14711a', 'b64::JC5yZXN1bHQudG9rZW4=::46b', 'when-expired', 600 %}", "description": ""}], "headers": [], "authentication": {}, "metaSortKey": -1741675671281, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "wrk_08fc208cb0084a0f97abc58ad2e4ba1f", "parentId": null, "modified": 1722392348283, "created": 1722392348283, "name": "PHPNuxBill", "description": "", "scope": "collection", "_type": "workspace"}, {"_id": "req_60bdc4349a2d47129e87c1ab3d14711a", "parentId": "wrk_08fc208cb0084a0f97abc58ad2e4ba1f", "modified": 1741676216255, "created": 1741675209738, "url": "{{ _.api_url }}", "name": "login", "description": "", "method": "POST", "body": {"mimeType": "application/x-www-form-urlencoded", "params": [{"id": "pair_637d448b0b374f2684c1a88f54669de4", "name": "username", "value": "admin", "description": ""}, {"id": "pair_14449ba043264407bdad04d166dabab9", "name": "password", "value": "admin", "description": ""}]}, "parameters": [{"id": "pair_b62a65cf20884c4b9694d82195a9d592", "name": "r", "value": "admin/post", "description": ""}], "headers": [{"name": "Content-Type", "value": "application/x-www-form-urlencoded"}], "authentication": {}, "metaSortKey": -1741675439941, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_adca9e44d68943c1b2b327df3cfe74ee", "parentId": "wrk_08fc208cb0084a0f97abc58ad2e4ba1f", "modified": 1741676218232, "created": 1741675745490, "url": "{{ _.api_url }}", "name": "dashboard", "description": "", "method": "GET", "body": {}, "parameters": [{"id": "pair_a5d79aa80cfe404cbb9cdcdcb18d23ef", "name": "r", "value": "dashboard", "description": ""}, {"id": "pair_d66de068199f43128d5690e27cf07fa0", "name": "token", "value": "{% response 'body', 'req_60bdc4349a2d47129e87c1ab3d14711a', 'b64::JC5yZXN1bHQudG9rZW4=::46b', 'when-expired', 600 %}", "description": ""}], "headers": [], "authentication": {}, "metaSortKey": -1741675324271, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_af22c9443acc41b4a1a11d28f07d7b75", "parentId": "fld_682b0477cf584c51b46c76f88626a95b", "modified": 1741677918253, "created": 1741676023272, "url": "{{ _.api_url }}", "name": "list", "description": "", "method": "GET", "body": {}, "parameters": [{"id": "pair_a5d79aa80cfe404cbb9cdcdcb18d23ef", "name": "r", "value": "customers", "description": ""}, {"id": "pair_d66de068199f43128d5690e27cf07fa0", "name": "token", "value": "{% response 'body', 'req_60bdc4349a2d47129e87c1ab3d14711a', 'b64::JC5yZXN1bHQudG9rZW4=::46b', 'when-expired', 600 %}", "description": ""}, {"name": "search", "value": "", "id": "pair_3914f88950304b4d9f369add2f240a0d"}, {"name": "order", "value": "username", "id": "pair_9d4a935df1d844a9b2e66625bd3b640a", "description": "username fullname lastname created_at"}, {"name": "filter", "value": "Active", "id": "pair_049ed0a57faf4efeb8230be8aa51185b", "description": "check result statuses"}, {"name": "orderby", "value": "asc", "id": "pair_b28d0c0d38754a0eb5d12bf8d4959d25", "description": "asc dsc"}, {"name": "p", "value": "1", "id": "pair_3dc7656a37144898bb4eb5622e53d95d", "description": "start from 1"}], "headers": [], "authentication": {}, "metaSortKey": -1740458260971, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_682b0477cf584c51b46c76f88626a95b", "parentId": "wrk_08fc208cb0084a0f97abc58ad2e4ba1f", "modified": 1741676212719, "created": 1741675208601, "name": "customers", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1741675208601, "_type": "request_group"}, {"_id": "req_0b90d08bb5744b5faf28b7467c885de3", "parentId": "fld_682b0477cf584c51b46c76f88626a95b", "modified": 1741676309494, "created": 1741676231087, "url": "{{ _.api_url }}", "name": "view activation", "description": "", "method": "GET", "body": {}, "parameters": [{"id": "pair_a5d79aa80cfe404cbb9cdcdcb18d23ef", "name": "r", "value": "customers/view/1/activation", "description": ""}, {"id": "pair_d66de068199f43128d5690e27cf07fa0", "name": "token", "value": "{% response 'body', 'req_60bdc4349a2d47129e87c1ab3d14711a', 'b64::JC5yZXN1bHQudG9rZW4=::46b', 'when-expired', 600 %}", "description": ""}, {"name": "p", "value": "1", "id": "pair_3dc7656a37144898bb4eb5622e53d95d"}], "headers": [], "authentication": {}, "metaSortKey": -1739849786612.5, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_4e2da7e9b44b4fa5ac9f71b671858a50", "parentId": "fld_682b0477cf584c51b46c76f88626a95b", "modified": 1741678442213, "created": 1741676293748, "url": "{{ _.api_url }}", "name": "view order", "description": "", "method": "GET", "body": {}, "parameters": [{"id": "pair_a5d79aa80cfe404cbb9cdcdcb18d23ef", "name": "r", "value": "customers/view/{% prompt 'Customer ID', 'id', '1', 'customer_id', false, true %}/order", "description": ""}, {"id": "pair_d66de068199f43128d5690e27cf07fa0", "name": "token", "value": "{% response 'body', 'req_60bdc4349a2d47129e87c1ab3d14711a', 'b64::JC5yZXN1bHQudG9rZW4=::46b', 'when-expired', 600 %}", "description": ""}, {"name": "p", "value": "1", "id": "pair_3dc7656a37144898bb4eb5622e53d95d"}], "headers": [], "authentication": {}, "metaSortKey": -1739545549433.25, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_bcea205bec4e4f2281adaa30c748127b", "parentId": "fld_682b0477cf584c51b46c76f88626a95b", "modified": 1741678215286, "created": 1741677744403, "url": "{{ _.api_url }}", "name": "add", "description": "", "method": "POST", "body": {"mimeType": "multipart/form-data", "params": [{"id": "pair_82956539392844e18a4d566f4cfe8453", "name": "username", "value": "", "description": ""}, {"id": "pair_9d5391c3b595498ca45c6e1f0ce4bf22", "name": "fullname", "value": "", "description": ""}, {"id": "pair_079109342c0c4312b0f1b7f59f123767", "name": "email", "value": "", "description": ""}, {"id": "pair_e32aaef0b26344aba71c240deeca21f3", "name": "phonenumber", "value": "", "description": ""}, {"id": "pair_993483915a1842278b459efe0d9b7d87", "name": "password", "value": "", "description": ""}, {"id": "pair_8ad5e531c4e24e96b520786c8d3c9dfb", "name": "address", "value": "", "description": ""}, {"id": "pair_0c819bcdfe154adcb8f0746a0f5d7ec6", "name": "service_type", "value": "Hotspot", "description": "Hotspot PPPoE VPN Others"}, {"id": "pair_564420f23dad4a4eaa5ab1da983cbea3", "name": "account_type", "value": "Personal", "description": "Personal Business"}, {"id": "pair_6c9b05ddfe8e497c822dd11b2ce53336", "name": "coordinates", "value": "-6.465422, 3.406448", "description": ""}, {"id": "pair_0a566c92232242269b62fe1301734a6e", "name": "pppoe_username", "value": "", "description": ""}, {"id": "pair_7d751a8c7b6245ee8476b54cccaa0369", "name": "pppoe_password", "value": "", "description": ""}, {"id": "pair_d67dbdc6f2f64e5395942474231b9861", "name": "pppoe_ip", "value": "", "description": ""}, {"id": "pair_fc20c43a80ac427c8030e3f52df7ca9a", "name": "send_welcome_message", "value": "1", "description": "", "disabled": true}, {"id": "pair_f342fe2bcd7f4446934d74974a422d32", "name": "sms", "value": "1", "description": "send_welcome_message", "disabled": true}, {"id": "pair_0a37da8e76964c5589f77333f22fb9a7", "name": "wa", "value": "1", "description": "send_welcome_message", "disabled": true}, {"id": "pair_5f571164a7914b7195b75fc006a90256", "name": "mail", "value": "1", "description": "send_welcome_message", "disabled": true}, {"id": "pair_da0b570ff3814b27972d7b6da82f3b50", "name": "city", "value": "Bandung", "description": ""}, {"id": "pair_3de758aa6663440b9e894e587ce799f0", "name": "district", "value": "<PERSON><PERSON><PERSON>", "description": ""}, {"id": "pair_f73e236cb3ab4d93996e10f5f28d492a", "name": "state", "value": "<PERSON>awa Barat", "description": ""}, {"id": "pair_b084a506eb1443ca98f673b86a5b8c41", "name": "zip", "value": "40534", "description": ""}]}, "parameters": [{"id": "pair_a5d79aa80cfe404cbb9cdcdcb18d23ef", "name": "r", "value": "customers/add-post", "description": ""}, {"id": "pair_d66de068199f43128d5690e27cf07fa0", "name": "token", "value": "{% response 'body', 'req_60bdc4349a2d47129e87c1ab3d14711a', 'b64::JC5yZXN1bHQudG9rZW4=::46b', 'when-expired', 600 %}", "description": ""}], "headers": [{"name": "Content-Type", "value": "multipart/form-data"}], "authentication": {}, "metaSortKey": -1739393430843.625, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_5d01a329a8104db5b541a945389de1c4", "parentId": "fld_682b0477cf584c51b46c76f88626a95b", "modified": 1741678313015, "created": 1741678219894, "url": "{{ _.api_url }}", "name": "edit", "description": "", "method": "POST", "body": {"mimeType": "multipart/form-data", "params": [{"id": "pair_82956539392844e18a4d566f4cfe8453", "name": "username", "value": "", "description": ""}, {"id": "pair_9d5391c3b595498ca45c6e1f0ce4bf22", "name": "fullname", "value": "", "description": ""}, {"id": "pair_079109342c0c4312b0f1b7f59f123767", "name": "email", "value": "", "description": ""}, {"id": "pair_e32aaef0b26344aba71c240deeca21f3", "name": "phonenumber", "value": "", "description": ""}, {"id": "pair_993483915a1842278b459efe0d9b7d87", "name": "password", "value": "", "description": ""}, {"id": "pair_8ad5e531c4e24e96b520786c8d3c9dfb", "name": "address", "value": "", "description": ""}, {"id": "pair_0c819bcdfe154adcb8f0746a0f5d7ec6", "name": "service_type", "value": "Hotspot", "description": "Hotspot PPPoE VPN Others"}, {"id": "pair_564420f23dad4a4eaa5ab1da983cbea3", "name": "account_type", "value": "Personal", "description": "Personal Business"}, {"id": "pair_6c9b05ddfe8e497c822dd11b2ce53336", "name": "coordinates", "value": "-6.465422, 3.406448", "description": ""}, {"id": "pair_0a566c92232242269b62fe1301734a6e", "name": "pppoe_username", "value": "", "description": ""}, {"id": "pair_7d751a8c7b6245ee8476b54cccaa0369", "name": "pppoe_password", "value": "", "description": ""}, {"id": "pair_d67dbdc6f2f64e5395942474231b9861", "name": "pppoe_ip", "value": "", "description": ""}, {"id": "pair_fc20c43a80ac427c8030e3f52df7ca9a", "name": "send_welcome_message", "value": "1", "description": "", "disabled": true}, {"id": "pair_f342fe2bcd7f4446934d74974a422d32", "name": "sms", "value": "1", "description": "send_welcome_message", "disabled": true}, {"id": "pair_0a37da8e76964c5589f77333f22fb9a7", "name": "wa", "value": "1", "description": "send_welcome_message", "disabled": true}, {"id": "pair_5f571164a7914b7195b75fc006a90256", "name": "mail", "value": "1", "description": "send_welcome_message", "disabled": true}, {"id": "pair_da0b570ff3814b27972d7b6da82f3b50", "name": "city", "value": "Bandung", "description": ""}, {"id": "pair_3de758aa6663440b9e894e587ce799f0", "name": "district", "value": "<PERSON><PERSON><PERSON>", "description": ""}, {"id": "pair_f73e236cb3ab4d93996e10f5f28d492a", "name": "state", "value": "<PERSON>awa Barat", "description": ""}, {"id": "pair_b084a506eb1443ca98f673b86a5b8c41", "name": "zip", "value": "40534", "description": ""}, {"id": "pair_53c1252c07fb40c8a8f0bec9b5d9cc3c", "name": "id", "value": "", "description": "customer ID"}]}, "parameters": [{"id": "pair_a5d79aa80cfe404cbb9cdcdcb18d23ef", "name": "r", "value": "customers/edit-post", "description": ""}, {"id": "pair_d66de068199f43128d5690e27cf07fa0", "name": "token", "value": "{% response 'body', 'req_60bdc4349a2d47129e87c1ab3d14711a', 'b64::JC5yZXN1bHQudG9rZW4=::46b', 'when-expired', 600 %}", "description": ""}], "headers": [{"name": "Content-Type", "value": "multipart/form-data"}], "authentication": {}, "metaSortKey": -1739317371548.8125, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_d1b878590c384970b0fd76532f3a0560", "parentId": "fld_682b0477cf584c51b46c76f88626a95b", "modified": 1741678547932, "created": 1741678464770, "url": "{{ _.api_url }}", "name": "delete", "description": "", "method": "GET", "body": {"mimeType": "multipart/form-data", "params": []}, "parameters": [{"id": "pair_a5d79aa80cfe404cbb9cdcdcb18d23ef", "name": "r", "value": "customers/delete/{% prompt 'Delete Customer', 'Customer ID', '1', 'customer_id', false, true %}", "description": ""}, {"id": "pair_d66de068199f43128d5690e27cf07fa0", "name": "token", "value": "{% response 'body', 'req_60bdc4349a2d47129e87c1ab3d14711a', 'b64::JC5yZXN1bHQudG9rZW4=::46b', 'when-expired', 600 %}", "description": ""}], "headers": [{"name": "Content-Type", "value": "multipart/form-data"}], "authentication": {}, "metaSortKey": -1739279341901.4062, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "env_5e5440f3ed0befaf534143bec5b0cdcff3a045fc", "parentId": "wrk_08fc208cb0084a0f97abc58ad2e4ba1f", "modified": 1741675272973, "created": 1722392348289, "name": "Base Environment", "data": {"api_url": "https://nuxbill.ibnux.com/system/api.php"}, "dataPropertyOrder": {"&": ["api_url"]}, "color": null, "isPrivate": false, "metaSortKey": 1722392348289, "_type": "environment"}, {"_id": "jar_5e5440f3ed0befaf534143bec5b0cdcff3a045fc", "parentId": "wrk_08fc208cb0084a0f97abc58ad2e4ba1f", "modified": 1741678205393, "created": 1722392348290, "name": "<PERSON><PERSON><PERSON>", "cookies": [{"key": "PHPSESSID", "value": "d8kfbchmuf1q1dpcm9e3s28sp7", "domain": "phpmixbill.ibnux.org", "path": "/", "hostOnly": true, "creation": "2024-07-31T02:19:53.181Z", "lastAccessed": "2024-07-31T02:19:53.181Z", "id": "5563233410191446"}, {"key": "aid", "value": "1.1741678205.1360dae24ca2df459bb3a0f6b6d7903c0ca53a80", "expires": "2025-03-18T07:30:05.000Z", "maxAge": 604800, "domain": "phpmixbill.ibnux.org", "path": "/", "httpOnly": true, "extensions": ["SameSite=Lax"], "hostOnly": true, "creation": "2025-03-11T06:47:16.907Z", "lastAccessed": "2025-03-11T07:30:05.393Z", "id": "55636410862489"}], "_type": "cookie_jar"}, {"_id": "spc_346a80f2cbc84a4ca164fd0b2a171ec1", "parentId": "wrk_08fc208cb0084a0f97abc58ad2e4ba1f", "modified": 1722392348284, "created": 1722392348284, "fileName": "PHPNuxBill", "contents": "", "contentType": "yaml", "_type": "api_spec"}, {"_id": "env_13bf6070e4c846e6a4551b776654fff6", "parentId": "env_5e5440f3ed0befaf534143bec5b0cdcff3a045fc", "modified": 1741675301149, "created": 1741675273757, "name": "nuxbill.ibnux.com", "data": {"api_url": "https://nuxbill.ibnux.com/system/api.php"}, "dataPropertyOrder": {"&": ["api_url"]}, "color": null, "isPrivate": false, "metaSortKey": 1741675273757, "_type": "environment"}, {"_id": "env_8c0a213472744d3bafd756bf1d04d6d9", "parentId": "env_5e5440f3ed0befaf534143bec5b0cdcff3a045fc", "modified": 1741675342707, "created": 1741675303006, "name": "phpmixbill.ibnux.org", "data": {"api_url": "http://phpmixbill.ibnux.org/system/api.php"}, "dataPropertyOrder": {"&": ["api_url"]}, "color": null, "isPrivate": false, "metaSortKey": 1741675303006, "_type": "environment"}]}