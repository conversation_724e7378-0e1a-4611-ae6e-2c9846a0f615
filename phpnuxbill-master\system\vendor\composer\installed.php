<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => '925c24cbd822f776eb913df987a063f95c6d9cc0',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '925c24cbd822f776eb913df987a063f95c6d9cc0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mpdf/mpdf' => array(
            'pretty_version' => 'v8.1.6',
            'version' => '8.1.6.0',
            'reference' => '146c7c1dfd21c826b9d5bbfe3c15e52fd933c90f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mpdf/mpdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mpdf/psr-log-aware-trait' => array(
            'pretty_version' => 'v2.0.0',
            'version' => '2.0.0.0',
            'reference' => '7a077416e8f39eb626dee4246e0af99dd9ace275',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mpdf/psr-log-aware-trait',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'myclabs/deep-copy' => array(
            'pretty_version' => '1.11.1',
            'version' => '1.11.1.0',
            'reference' => '7284c22080590fb39f2ffa3e9057f10a4ddd0e0c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/deep-copy',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.100',
            'version' => '9.99.100.0',
            'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message' => array(
            'pretty_version' => '1.1',
            'version' => '1.1.0.0',
            'reference' => 'cb6ce4845ce34a8ad9e68117c10ee90a29919eba',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'setasign/fpdi' => array(
            'pretty_version' => 'v2.3.7',
            'version' => '2.3.7.0',
            'reference' => 'bccc892d5fa1f48c43f8ba7db5ed4ba6f30c8c05',
            'type' => 'library',
            'install_path' => __DIR__ . '/../setasign/fpdi',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'smarty/smarty' => array(
            'pretty_version' => 'v4.5.3',
            'version' => '4.5.3.0',
            'reference' => '9fc96a13dbaf546c3d7bcf95466726578cd4e0fa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../smarty/smarty',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'yosiazwan/php-facedetection' => array(
            'pretty_version' => '0.1.0',
            'version' => '0.1.0.0',
            'reference' => 'b016273ceceacd85562bbc50384fbabc947fe525',
            'type' => 'library',
            'install_path' => __DIR__ . '/../yosiazwan/php-facedetection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
