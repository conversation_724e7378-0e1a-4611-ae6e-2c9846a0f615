package com.phpnuxbill.mobile;

import android.content.Context;
import android.content.SharedPreferences;
import android.webkit.CookieManager;

public class SessionManager {
    private static final String PREF_NAME = "PHPNuxBillSession";
    private static final String KEY_IS_LOGGED_IN = "isLoggedIn";
    private static final String KEY_USERNAME = "username";
    private static final String KEY_USER_TYPE = "userType";

    private SharedPreferences pref;
    private SharedPreferences.Editor editor;
    private Context context;

    public SessionManager(Context context) {
        this.context = context;
        pref = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        editor = pref.edit();
    }

    /**
     * Store login status and user info
     */
    public void setLogin(boolean isLoggedIn, String username, String userType) {
        editor.putBoolean(KEY_IS_LOGGED_IN, isLoggedIn);
        editor.putString(KEY_USERNAME, username);
        editor.putString(KEY_USER_TYPE, userType);
        editor.commit();
    }

    /**
     * Check if user is logged in
     */
    public boolean isLoggedIn() {
        return pref.getBoolean(KEY_IS_LOGGED_IN, false);
    }

    /**
     * Get stored username
     */
    public String getUsername() {
        return pref.getString(KEY_USERNAME, "");
    }

    /**
     * Get user type (admin, customer, etc.)
     */
    public String getUserType() {
        return pref.getString(KEY_USER_TYPE, "");
    }

    /**
     * Clear session and logout
     */
    public void logout() {
        // Clear all data from SharedPreferences
        editor.clear();
        editor.commit();

        // Clear cookies
        CookieManager cookieManager = CookieManager.getInstance();
        cookieManager.removeAllCookies(null);
        cookieManager.flush();
    }
}
