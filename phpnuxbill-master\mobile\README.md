# PHPNuxBill Mobile App

This is a mobile application for PHPNuxBill, a Mikrotik Billing platform. The app provides a mobile-friendly interface to access and manage your PHPNuxBill installation.

## Features

- Access PHPNuxBill from your mobile device
- Login as admin or customer
- View and manage billing information
- Responsive design for mobile screens
- Offline detection and handling
- Session management
- File upload support

## Requirements

- Android 5.0 (Lollipop) or higher
- Internet connection to access your PHPNuxBill server

## Setup Instructions

1. Open the project in Android Studio
2. Edit the `baseUrl` variable in `MainActivity.java` to point to your PHPNuxBill installation
3. Build and run the application

## Configuration

Before building the app, make sure to update the following:

1. In `MainActivity.java`, change the `baseUrl` to your PHPNuxBill URL:
   ```java
   private String baseUrl = "http://your-phpnuxbill-url.com"; // Change this to your PHPNuxBill URL
   ```

2. Replace the logo image in the `res/drawable` folder with your own logo

## Building the App

To build the app:

1. Open the project in Android Studio
2. Select Build > Build Bundle(s) / APK(s) > Build APK(s)
3. The APK will be generated in the `app/build/outputs/apk/debug/` directory

## Customization

You can customize the app by:

- Changing colors in `colors.xml`
- Updating app name in `strings.xml`
- Modifying the splash screen in `activity_splash.xml`
- Adding additional features to the WebView in `MainActivity.java`

## Security Considerations

- The app uses WebView to display your PHPNuxBill installation
- Make sure your PHPNuxBill installation is secured with HTTPS
- User credentials are handled by your PHPNuxBill server, not stored in the app

## License

This mobile app is released under the same license as PHPNuxBill.
