# PHPNuxBill Mobile App Setup Guide

This guide will help you set up and customize the PHPNuxBill mobile app for your specific installation.

## Prerequisites

Before you begin, make sure you have:

1. Android Studio installed on your computer
2. A working PHPNuxBill installation accessible via the internet
3. Basic knowledge of Android development

## Step 1: Clone or Download the Project

Clone this repository or download the source code to your local machine.

## Step 2: Open the Project in Android Studio

1. Open Android Studio
2. Select "Open an existing Android Studio project"
3. Navigate to the downloaded project folder and select it

## Step 3: Configure Your PHPNuxBill URL

1. Open `MainActivity.java`
2. Find the line with `private String baseUrl = "http://your-phpnuxbill-url.com";`
3. Replace the URL with your actual PHPNuxBill installation URL
4. Make sure your URL is accessible from the internet

## Step 4: Customize App Appearance

### App Name
1. Open `res/values/strings.xml`
2. Change the value of `app_name` to your desired name

### App Colors
1. Open `res/values/colors.xml`
2. Modify the color values to match your branding

### App Logo
1. Replace the logo files in the `res/mipmap` folders with your own logo
2. Make sure to provide different sizes for different screen densities

## Step 5: Build and Test the App

1. Connect an Android device to your computer or set up an emulator
2. Click the "Run" button in Android Studio
3. Select your device/emulator and click "OK"
4. The app should build and install on your device

## Step 6: Troubleshooting

### App shows blank screen
- Check if your PHPNuxBill URL is accessible
- Make sure you've enabled internet permission in the manifest
- Check if your PHPNuxBill installation requires HTTPS

### WebView not loading correctly
- Try enabling JavaScript debugging in WebView
- Add the following code to your Application class:
  ```java
  if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
      WebView.setWebContentsDebuggingEnabled(true);
  }
  ```

### Login issues
- Make sure your PHPNuxBill installation is working correctly
- Check if cookies are enabled in the WebView

## Step 7: Preparing for Distribution

### Generate a Signed APK
1. In Android Studio, select Build > Generate Signed Bundle/APK
2. Follow the wizard to create a signing key if you don't have one
3. Select APK and follow the steps to generate a signed APK

### Distribute the App
You can distribute your app through:
- Google Play Store (requires developer account)
- Direct APK distribution to your users
- Enterprise distribution methods

## Additional Customization

### Adding Push Notifications
To add push notifications for important events:

1. Integrate Firebase Cloud Messaging (FCM)
2. Modify your PHPNuxBill installation to send notifications via FCM
3. Implement a notification receiver in the app

### Offline Mode
To enhance offline capabilities:

1. Implement caching of important data
2. Store user session information securely
3. Add offline indicators and automatic reconnection

## Support

If you need help with the mobile app, please refer to:
- PHPNuxBill documentation
- Android WebView documentation
- Contact the PHPNuxBill community for assistance
