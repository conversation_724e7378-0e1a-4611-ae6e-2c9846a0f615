{"packages": [{"name": "mpdf/mpdf", "version": "v8.1.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/mpdf/mpdf.git", "reference": "146c7c1dfd21c826b9d5bbfe3c15e52fd933c90f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/mpdf/zipball/146c7c1dfd21c826b9d5bbfe3c15e52fd933c90f", "reference": "146c7c1dfd21c826b9d5bbfe3c15e52fd933c90f", "shasum": ""}, "require": {"ext-gd": "*", "ext-mbstring": "*", "mpdf/psr-log-aware-trait": "^2.0 || ^3.0", "myclabs/deep-copy": "^1.7", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": "^5.6 || ^7.0 || ~8.0.0 || ~8.1.0 || ~8.2.0", "psr/http-message": "^1.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "setasign/fpdi": "^2.1"}, "require-dev": {"mockery/mockery": "^1.3.0", "mpdf/qrcode": "^1.1.0", "squizlabs/php_codesniffer": "^3.5.0", "tracy/tracy": "~2.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-bcmath": "Needed for generation of some types of barcodes", "ext-xml": "Needed mainly for SVG manipulation", "ext-zlib": "Needed for compression of embedded resources, such as fonts"}, "time": "2023-05-03T19:36:43+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Mpdf\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-only"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>, maintainer"}, {"name": "<PERSON>", "role": "<PERSON><PERSON><PERSON> (retired)"}], "description": "PHP library generating PDF files from UTF-8 encoded HTML", "homepage": "https://mpdf.github.io", "keywords": ["pdf", "php", "utf-8"], "support": {"docs": "http://mpdf.github.io", "issues": "https://github.com/mpdf/mpdf/issues", "source": "https://github.com/mpdf/mpdf"}, "funding": [{"url": "https://www.paypal.me/mpdf", "type": "custom"}], "install-path": "../mpdf/mpdf"}, {"name": "mpdf/psr-log-aware-trait", "version": "v2.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/mpdf/psr-log-aware-trait.git", "reference": "7a077416e8f39eb626dee4246e0af99dd9ace275"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/psr-log-aware-trait/zipball/7a077416e8f39eb626dee4246e0af99dd9ace275", "reference": "7a077416e8f39eb626dee4246e0af99dd9ace275", "shasum": ""}, "require": {"psr/log": "^1.0 || ^2.0"}, "time": "2023-05-03T06:18:28+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Mpdf\\PsrLogAwareTrait\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Trait to allow support of different psr/log versions.", "support": {"issues": "https://github.com/mpdf/psr-log-aware-trait/issues", "source": "https://github.com/mpdf/psr-log-aware-trait/tree/v2.0.0"}, "install-path": "../mpdf/psr-log-aware-trait"}, {"name": "myclabs/deep-copy", "version": "1.11.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "7284c22080590fb39f2ffa3e9057f10a4ddd0e0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/7284c22080590fb39f2ffa3e9057f10a4ddd0e0c", "reference": "7284c22080590fb39f2ffa3e9057f10a4ddd0e0c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3,<3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "time": "2023-03-08T13:26:56+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.11.1"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "install-path": "../myclabs/deep-copy"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "version_normalized": "**********", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "time": "2020-10-15T08:29:30+00:00", "type": "library", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "install-path": "../paragonie/random_compat"}, {"name": "psr/http-message", "version": "1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "time": "2023-04-04T09:50:52+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "install-path": "../psr/http-message"}, {"name": "psr/log", "version": "1.1.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2021-05-03T11:20:27+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "install-path": "../psr/log"}, {"name": "setasign/fpdi", "version": "v2.3.7", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "bccc892d5fa1f48c43f8ba7db5ed4ba6f30c8c05"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/bccc892d5fa1f48c43f8ba7db5ed4ba6f30c8c05", "reference": "bccc892d5fa1f48c43f8ba7db5ed4ba6f30c8c05", "shasum": ""}, "require": {"ext-zlib": "*", "php": "^5.6 || ^7.0 || ^8.0"}, "conflict": {"setasign/tfpdf": "<1.31"}, "require-dev": {"phpunit/phpunit": "~5.7", "setasign/fpdf": "~1.8", "setasign/tfpdf": "1.31", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "~6.2"}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use TCPDF or tFPDF as an alternative. There's no fixed dependency configured."}, "time": "2023-02-09T10:38:43+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"setasign\\Fpdi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "support": {"issues": "https://github.com/Setasign/FPDI/issues", "source": "https://github.com/Setasign/FPDI/tree/v2.3.7"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/setasign/fpdi", "type": "tidelift"}], "install-path": "../setasign/fpdi"}, {"name": "smarty/smarty", "version": "v4.5.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/smarty-php/smarty.git", "reference": "9fc96a13dbaf546c3d7bcf95466726578cd4e0fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/smarty-php/smarty/zipball/9fc96a13dbaf546c3d7bcf95466726578cd4e0fa", "reference": "9fc96a13dbaf546c3d7bcf95466726578cd4e0fa", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^7.5", "smarty/smarty-lexer": "^3.1"}, "time": "2024-05-28T21:46:01+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["libs/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://www.iwink.nl/"}], "description": "Smarty - the compiling PHP template engine", "homepage": "https://smarty-php.github.io/smarty/", "keywords": ["templating"], "support": {"forum": "https://github.com/smarty-php/smarty/discussions", "issues": "https://github.com/smarty-php/smarty/issues", "source": "https://github.com/smarty-php/smarty/tree/v4.5.3"}, "install-path": "../smarty/smarty"}, {"name": "yosiazwan/php-facedetection", "version": "0.1.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/yosiazwan/php-facedetection.git", "reference": "b016273ceceacd85562bbc50384fbabc947fe525"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yosiazwan/php-facedetection/zipball/b016273ceceacd85562bbc50384fbabc947fe525", "reference": "b016273ceceacd85562bbc50384fbabc947fe525", "shasum": ""}, "require": {"ext-gd": "*", "php": ">=5.2.0"}, "time": "2016-01-26T22:10:00+00:00", "type": "library", "installation-source": "source", "autoload": {"classmap": ["FaceDetector.php", "Exception/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/mauricesvay/php-facedetection/graphs/contributors"}], "description": "PHP class to detect one face in images. A pure PHP port of an existing JS code from Ka<PERSON>ik Tharavad.", "homepage": "https://github.com/mauricesvay/php-facedetection", "support": {"source": "https://github.com/yosiazwan/php-facedetection/tree/0.1.0"}, "install-path": "../yosiazwan/php-facedetection"}], "dev": true, "dev-package-names": []}